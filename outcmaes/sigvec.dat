% # columns="iteration, evaluation, sigma, beta, void,  sigvec==sigma_vec.scaling factors from diagonal decoding", seed=1077697, Sat Aug 23 20:31:06 2025, <python>{}</python>
1 20 0.2966784101742684 1 0 0.8 1.0 1.0
2 40 0.3336855223013606 1 0 0.8 1.0 1.0
3 60 0.4115449353697537 1 0 0.8 1.0 1.0
4 80 0.4532557931628865 1 0 0.8 1.0 1.0
5 100 0.5453598830401067 1 0 0.8 1.0 1.0
6 120 0.8061157599081595 1 0 0.8 1.0 1.0
7 140 1.0103468545578598 1 0 0.8 1.0 1.0
8 160 1.132470996893887 1 0 0.8 1.0 1.0
9 180 1.0081377054824932 1 0 0.8 1.0 1.0
10 200 1.2276196843962202 1 0 0.8 1.0 1.0
11 220 1.1147543482458535 1 0 0.8 1.0 1.0
12 240 1.2763979852961538 1 0 0.8 1.0 1.0
13 260 1.9852722686966973 1 0 0.8 1.0 1.0
14 280 2.1278835199989605 1 0 0.8 1.0 1.0
15 300 2.115452025297316 1 0 0.8 1.0 1.0
16 320 1.971139860320803 1 0 0.8 1.0 1.0
17 340 1.9871342439115172 1 0 0.8 1.0 1.0
18 360 1.8866757226483684 1 0 0.8 1.0 1.0
19 380 1.4667024965155848 1 0 0.8 1.0 1.0
20 400 1.3143191776075684 1 0 0.8 1.0 1.0
21 420 1.1167999425345991 1 0 0.8 1.0 1.0
22 440 0.8876174564400575 1 0 0.8 1.0 1.0
23 460 0.7087295188315084 1 0 0.8 1.0 1.0
24 480 0.6584699240633417 1 0 0.8 1.0 1.0
25 500 0.7563913385795339 1 0 0.8 1.0 1.0
26 520 0.6812982552098904 1 0 0.8 1.0 1.0
27 540 0.5822797792429772 1 0 0.8 1.0 1.0
28 560 0.4934841427489337 1 0 0.8 1.0 1.0
29 580 0.43730726356842725 1 0 0.8 1.0 1.0
30 600 0.39669244523076935 1 0 0.8 1.0 1.0
31 620 0.29814991927773743 1 0 0.8 1.0 1.0
32 640 0.24296557123444656 1 0 0.8 1.0 1.0
33 660 0.1885352903032229 1 0 0.8 1.0 1.0
34 680 0.14289345840145645 1 0 0.8 1.0 1.0
35 700 0.12645136438168633 1 0 0.8 1.0 1.0
36 720 0.10994373859648374 1 0 0.8 1.0 1.0
37 740 0.08326910872955039 1 0 0.8 1.0 1.0
